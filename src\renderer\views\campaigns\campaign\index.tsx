/* eslint-disable react/no-array-index-key */
import { useParams, useNavigate } from 'react-router-dom';
import { Grid, Box } from '@mui/material';

import CampaignDetailSkeleton from '../../../components/skeletons/CampaignDetailSkeleton';

// Refactored components
import DetailHeader from '../../../components/DetailHeader';
import ErrorState from '../../../components/ErrorState';
import CampaignInfoSection from '../components/CampaignInfoSection';
import CampaignConfigSection from '../components/CampaignConfigSection';
import CampaignGroupsSection from '../components/CampaignGroupsSection';
import CampaignImagesSection from '../components/CampaignImagesSection';
import CampaignProgressLogs from '../components/CampaignProgressLogs';

// Custom hooks
import { useCampaignDetail } from '../hooks/useCampaignDetail';
import { getCampaignDetailStatusConfig } from '../../../../utils/campaignStatus';

// Utility functions are now in src/utils/campaignStatus.ts

export default function CampaignDetail() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Custom hook for data management
  const { campaignDetail, loading, error, logs } = useCampaignDetail(id);

  // Loading state
  if (loading) {
    return <CampaignDetailSkeleton />;
  }

  // Error state
  if (error) {
    return (
      <ErrorState
        error={error}
        onBack={() => navigate('/campaign/all')}
        severity="error"
      />
    );
  }

  // No data state
  if (!campaignDetail) {
    return (
      <ErrorState
        error="Không tìm thấy thông tin chiến dịch."
        onBack={() => navigate('/campaign/all')}
        severity="warning"
      />
    );
  }

  const { Campaign, CampaignConfiguration, Group, Image } = campaignDetail;
  const statusConfig = getCampaignDetailStatusConfig(Campaign?.status);

  return (
    <Box>
      {/* Header Section */}
      <DetailHeader
        title={`Chiến dịch: ${Campaign?.name || 'Đang tải thông tin...'}`}
        status={Campaign?.status}
        statusConfig={statusConfig}
        onBack={() => navigate(-1)}
      />

      {/* Priority Log Section - Always Visible at Top on Mobile/Tablet */}
      <Grid container spacing={3}>
        {campaignDetail.Campaign?.status !== 'done' && (
          <Grid
            size={{ xs: 12, md: 12, lg: 4 }}
            sx={{ order: { xs: 1, lg: 2 } }}
          >
            <CampaignProgressLogs logs={logs} />
          </Grid>
        )}

        {/* Main Content - Responsive Layout */}
        <Grid
          size={{
            xs: 12,
            md: 12,
            lg: campaignDetail.Campaign?.status === 'done' ? 12 : 8,
          }}
          sx={{ order: { xs: 2, lg: 1 } }}
        >
          <Grid>
            {/* Campaign Information Section */}
            <Grid size={12}>
              {Campaign && <CampaignInfoSection campaign={Campaign} />}
            </Grid>

            {/* Campaign Configuration Section */}
            <Grid size={12}>
              <CampaignConfigSection
                configuration={CampaignConfiguration || null}
                campaign={Campaign!}
              />
            </Grid>

            {/* Images Section */}
            <Grid size={12}>
              <CampaignImagesSection images={Image || null} />
            </Grid>

            {/* Groups Section */}
            <Grid size={12}>
              <CampaignGroupsSection groups={Group || null} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
