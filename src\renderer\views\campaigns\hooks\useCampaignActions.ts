import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Campaign } from '../../../../interfaces/Campaigns';
import { CampaignType } from '../CampaignConfig';
import { useAlert } from '../../../hooks/AlertContext';
import { useModal } from '../../../hooks/ConfirmModalContext';
import { useCampaignContext } from '../context';
import { messages } from '../../../../utils/messages';

interface UseCampaignActionsProps {
  type?: CampaignType;
}

interface UseCampaignActionsReturn {
  selectedCampaign: Campaign | null;
  runningCampaigns: Record<string, boolean>;
  stoppingCampaigns: Record<string, boolean>;
  openAccountModal: boolean;
  openCampaignModal: boolean;
  setSelectedCampaign: (campaign: Campaign | null) => void;
  setOpenAccountModal: (open: boolean) => void;
  setOpenCampaignModal: (open: boolean) => void;
  handleClose: () => void;
  handleDelete: (id: string) => Promise<void>;
  onDelete: (campaign: Campaign) => void;
  handleSelectCampaign: (campaignType: CampaignType) => void;
  runCampaign: (campaign: Campaign) => Promise<void>;
  stopCampaign: (campaign: Campaign) => Promise<void>;
  handleCampaign: (campaign?: Campaign) => void;
  handleBulkDelete: (selectedIds: string[]) => Promise<boolean>;
}

/**
 * Custom hook for campaign actions and state management
 */
export const useCampaignActions = ({
  type,
}: UseCampaignActionsProps): UseCampaignActionsReturn => {
  const navigate = useNavigate();
  const { showAlert } = useAlert();
  const { openConfirmModal } = useModal();
  const { deleteCampaign, updateCampaign } = useCampaignContext();

  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(
    null,
  );
  const [runningCampaigns, setRunningCampaigns] = useState<
    Record<string, boolean>
  >({});
  const [stoppingCampaigns, setStoppingCampaigns] = useState<
    Record<string, boolean>
  >({});
  const [openAccountModal, setOpenAccountModal] = useState(false);
  const [openCampaignModal, setOpenCampaignModal] = useState(false);

  const handleClose = useCallback(() => {
    setOpenCampaignModal(false);
    setOpenAccountModal(false);
    setSelectedCampaign(null);
  }, []);

  const handleDelete = useCallback(
    async (id: string) => {
      try {
        const result = await window.Campaigns.deleteCampaign(id);
        if (result.success) {
          showAlert(messages.general.success, 'success');
          deleteCampaign(id);
        } else {
          showAlert(messages.general.error, 'error');
        }
      } catch (error) {
        showAlert(messages.general.error, 'error');
      }
    },
    [showAlert, deleteCampaign],
  );

  const onDelete = useCallback(
    (campaign: Campaign) => {
      const { id, name } = campaign;
      openConfirmModal({
        title: 'Xác nhận xóa',
        content: `Bạn có chắc muốn xóa chiến dịch "${name}" không?`,
        onSubmit: () => handleDelete(id),
        actions: { cancel: 'Hủy', submit: 'Xóa' },
      });
    },
    [openConfirmModal, handleDelete],
  );

  const handleSelectCampaign = useCallback(
    (campaignType: CampaignType) => {
      navigate(`/campaign/${campaignType}/add`);
    },
    [navigate],
  );

  const runCampaign = useCallback(
    async (campaign: Campaign) => {
      console.log('CampaignTable: Starting campaign run for:', campaign);
      const { id, status } = campaign;
      setRunningCampaigns((prev) => ({ ...prev, [id]: true }));

      if (status === 'new' || status === 'stopped') {
        const response = await window.account.getUserByStatus('active');
        const users = Array.isArray(response) ? response : [];
        if (users.length === 0) {
          showAlert(
            'Hiện chưa có tài khoản nào sẵn sàng. Vui lòng thêm mới tài khoản',
            'error',
          );
          return;
        }
        console.log('CampaignTable: Setting campaign to run:', campaign);
        setSelectedCampaign(campaign);
        setOpenAccountModal(true);
        return;
      }
      if (status === 'done') {
        showAlert('Chiến dịch đã kết thúc', 'success');
        return;
      }
      setStoppingCampaigns((prev) => {
        const updated = { ...prev };
        delete updated[id];
        return updated;
      });
    },
    [showAlert, updateCampaign, setSelectedCampaign, setOpenAccountModal],
  );

  const stopCampaign = useCallback(
    async (campaign: Campaign) => {
      const { id, type, name, message } = campaign;
      setStoppingCampaigns((prev) => ({ ...prev, [id]: true }));
      try {
        // Stop the campaign execution
        await window.facebookWeb.stopbycampaign(id);

        // Update campaign status to 'stopped' in the database
        const response = await window.Campaigns.updateCampaign({
          id,
          type,
          name,
          message,
          status: 'stopped',
        });

        if (response.success && response.data) {
          // Ensure the updated campaign has the correct status
          const updatedCampaign = {
            ...response.data,
            status: 'stopped',
          };
          updateCampaign(id, updatedCampaign);
          console.log('Campaign status updated in context:', updatedCampaign);
          showAlert('Chiến dịch đã được dừng thành công', 'success');
        } else {
          // If API update fails, still update local state for immediate UI feedback
          const fallbackCampaign = {
            ...campaign,
            status: 'stopped',
          };
          updateCampaign(id, fallbackCampaign);
          console.warn(
            'API update failed, using fallback local update:',
            fallbackCampaign,
          );
          showAlert(
            response.error || 'Lỗi khi cập nhật trạng thái chiến dịch',
            'error',
          );
        }
      } catch (error) {
        console.error('Error stopping campaign:', error);
        // Even if there's an error, try to update local state for UI consistency
        const fallbackCampaign = {
          ...campaign,
          status: 'stopped',
        };
        updateCampaign(id, fallbackCampaign);
        console.warn(
          'Error occurred, using fallback local update:',
          fallbackCampaign,
        );
        showAlert('Dừng chiến dịch thất bại', 'error');
      } finally {
        setStoppingCampaigns((prev) => {
          const updated = { ...prev };
          delete updated[id];
          return updated;
        });
        // Also clear running state to ensure UI updates properly
        setRunningCampaigns((prev) => {
          const updated = { ...prev };
          delete updated[id];
          return updated;
        });
      }
    },
    [showAlert, updateCampaign],
  );

  const handleCampaign = useCallback(
    (campaign?: Campaign) => {
      if (campaign?.id) {
        navigate(`/campaign/${campaign.type}/${campaign.id}/edit`);
      } else if (type) {
        navigate(`/campaign/${type}/add`);
      } else {
        setOpenCampaignModal(true);
      }
    },
    [navigate, type],
  );

  const handleBulkDelete = useCallback(
    async (selectedIds: string[]): Promise<boolean> => {
      if (selectedIds.length === 0) return false;

      // Check if any selected campaigns are currently running
      const runningIds = selectedIds.filter((id) => runningCampaigns[id]);
      console.log('Running campaigns:', runningIds);
      if (runningIds.length > 0) {
        showAlert(
          `Không thể xóa ${runningIds.length} chiến dịch đang chạy. Vui lòng dừng chiến dịch trước khi xóa.`,
          'error',
        );
        return false;
      }

      try {
        const result = await window.Campaigns.bulkDeleteCampaigns(selectedIds);

        if (result) {
          // Update local state for each deleted campaign
          let successCount = 0;
          selectedIds.forEach((id) => {
            try {
              deleteCampaign(id);
              successCount++;
            } catch (error) {
              console.error(
                `Failed to remove campaign ${id} from local state:`,
                error,
              );
            }
          });
          if (successCount === selectedIds.length) {
            showAlert(
              `Đã xóa ${selectedIds.length} chiến dịch thành công`,
              'success',
            );
            console.log('All campaigns successfully removed from local state');
            return true;
          } else {
            showAlert(
              `Đã xóa ${successCount}/${selectedIds.length} chiến dịch. Một số chiến dịch có thể cần làm mới trang.`,
              'warning',
            );
            return false;
          }
        } else {
          showAlert('Không thể xóa chiến dịch. Vui lòng thử lại.', 'error');
          return false;
        }
      } catch (error) {
        console.error('Error during bulk delete:', error);
        showAlert(
          'Có lỗi xảy ra khi xóa chiến dịch. Vui lòng thử lại.',
          'error',
        );
        return false;
      }
    },
    [showAlert, deleteCampaign, runningCampaigns],
  );

  return {
    selectedCampaign,
    runningCampaigns,
    stoppingCampaigns,
    openAccountModal,
    openCampaignModal,
    setSelectedCampaign,
    setOpenAccountModal,
    setOpenCampaignModal,
    handleClose,
    handleDelete,
    onDelete,
    handleSelectCampaign,
    runCampaign,
    stopCampaign,
    handleCampaign,
    handleBulkDelete,
  };
};
