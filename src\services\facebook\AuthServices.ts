import fs from 'fs-extra';
import { Page } from 'puppeteer-core';
import FacebookBrowser from './FacebookBrowser';
import log from '../../utils/logs';
import { extractUsernameFromUrl, typeToInput } from '../../utils/facebook';
import { FBLogged, FBUserLoginResponse } from '../../types/FacebookUser';
import { delay } from '../../utils/helper';

export default class AuthServices extends FacebookBrowser {
  private emailSelector: string = 'input[name="email"]';

  private passSelector: string = 'input[name="pass"]';

  private loginSelector: string = 'button[name="login"]';

  /**
   * Handle login
   * @returns
   */
  async login(data: {username: string, password: string, profileId: string}): Promise<FBUserLoginResponse> {
    const { username, password, profileId } = data;
    let page = null;

    try {
      page = await this.launchBrowser(profileId, false);
      if (!page) {
        log.error('Browser not found');
        return { success: false, error: '<PERSON>hông tìm thấy trình duyệt' };
      }

    } catch (error) {
      log.error('An error occurred on initializing browser', error)
      return { success: false, error: 'Không tim thấy trang đăng nhập' };
    }

    try {

      await page.goto(this.facebookUrl, {
        waitUntil: 'networkidle2',
        timeout: 5000,
      });

      const inputEmail = await page.$(this.emailSelector);
      const inputPass = await page.$(this.passSelector);
      const loginBtn = await page.$(this.loginSelector);
      if (inputEmail && inputPass && loginBtn) {
        await typeToInput(page, this.emailSelector, username);
        await typeToInput(page, this.passSelector, password);
        await page.click(this.loginSelector);
      }
    } catch (error) {
      log.error('An error occurred when start login facebook', error)
      return { success: false, error: 'Đã xảy ra lỗi trong quá trình đăng nhập' };
    }

    try {
      // wait for login success and c_user is set in cookies
      await page.waitForFunction(
        () => {
          const cookies = document.cookie.split('; ');
          return cookies.some(cookie => cookie.startsWith('c_user='));
        },
        { timeout: 300000 }
      );
    } catch (error) {
      log.error('Get user cookies error', error);
      return { success: false, error: 'Đăng nhập thất bại' };
    }

    const userId = await AuthServices.getUserUid(page);
    if (userId === '') {
      await FacebookBrowser.close(page);
      return {
        success: false,
        error: 'Đăng nhập thất bại',
      };
    }

    await FacebookBrowser.close(page);
    return { success: true, userId };
  }

  /**
   * Get logged facebook page
   * @returns page or null
   */
  async getLoggedPage(profileId: string, headless: boolean = false): Promise<FBLogged> {
    const page = await this.launchBrowser(profileId, headless);
    if (!page) {
      log.error('Profile directory not found', { profileId });

      return {
        success: false,
        page: null,
        error: 'Profile directory not found'
      };
    }

    try {
      await page.goto(this.facebookUrl, { waitUntil: 'networkidle2', timeout: 10000 });
      const title = await page.title();
      if (!title || title !== 'Facebook') {
        await page.reload({timeout: 10000});
      }

      const userId = await AuthServices.getUserUid(page);
      if (userId === '') {
        return {
          success: false,
          page,
          error: 'Session login expired'
        };
      }

      // check user has nickname
      await page.goto(`${this.facebookUrl}/profile.php?id=${userId}`,{ waitUntil: 'networkidle2', timeout: 10000 });
      await delay(1000);
      const url = page.url();
      return { success: true, page, user: { userId, profilePath: url } };

    } catch {
      log.error('Session login expired', { profileId });
      return {
        success: false,
        page,
        error: 'Session login expired'
      };
    }
  }

  /**
   * Delete profile dir
   * @param profileId
   */
  static async deleteProfile(profileId: string) {
    const profilePath = FacebookBrowser.getProfileDir(profileId);
    try {
      await fs.remove(profilePath);
    } catch (error) {
      log.error('Error when remove profile dir: ', { profileId, error });
    }
  }

  /**
   * Get user cookies if login success
   * @param page
   * @returns
   */
  static async getUserUid(page: Page) {
    const cookies = await page.browserContext().cookies();
    const cUserCookie = cookies.find((cookie) => cookie.name === 'c_user');
    if (cUserCookie) {
      return cUserCookie.value;
    }
    return '';
  }

  /**
   * Open Facebook browser
   * @param profileId
   * @param headless 
   * @returns 
   */
  async launchProfilePage(profileId: string, headless: boolean = false) {
    const page = await this.launchBrowser(profileId, headless);
    if (!page) {
      log.error('Profile directory not found', { profileId });

      return {
        success: false,
        page: null,
        error: 'Profile directory not found'
      };
    }

    await page.goto(this.facebookUrl, { waitUntil: 'domcontentloaded', timeout: 5000 });
    return { success: true, page };
  }
}
